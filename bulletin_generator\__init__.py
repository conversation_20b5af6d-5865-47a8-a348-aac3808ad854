"""
Church Bulletin Generator

A Python application for automated generation of church bulletins
from templates and database content.
"""

__version__ = "1.0.0"
__author__ = "Church Bulletin Generator Team"
__email__ = "<EMAIL>"

from .core.database import BulletinDatabase
from .core.template_parser import TemplateParser
from .core.content_generator import ContentGenerator
from .models.bulletin import BulletinData
from .models.service import ServiceTime
from .models.event import Event

__all__ = [
    "BulletinDatabase",
    "TemplateParser", 
    "ContentGenerator",
    "BulletinData",
    "ServiceTime",
    "Event"
]
