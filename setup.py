#!/usr/bin/env python3
"""
Setup script for Church Bulletin Generator
"""

import shutil
from pathlib import Path
from bulletin_generator.config import ensure_directories, get_config

def setup_project():
    """Setup project directories and copy templates"""
    print("Setting up Church Bulletin Generator...")
    
    # Ensure directories exist
    ensure_directories()
    print("✓ Created project directories")
    
    # Get config
    config = get_config()
    templates_dir = config['paths']['templates_dir']
    
    # Copy existing SVG template
    existing_template = Path("Bulletin 040825/Bulletin 030825.svg")
    if existing_template.exists():
        target_template = templates_dir / "Bulletin 030825.svg"
        shutil.copy2(existing_template, target_template)
        print(f"✓ Copied template to {target_template}")
    else:
        print("⚠ Warning: Could not find existing template file")
    
    # Check database schema
    schema_source = Path("database_schema.sql")
    if schema_source.exists():
        print(f"✓ Database schema found at {schema_source.absolute()}")
    else:
        print("⚠ Warning: Could not find database schema file")
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Initialize database: python main.py init-db")
    print("3. Check status: python main.py status")
    print("4. Generate bulletin: python main.py generate --date 2025-08-10")

if __name__ == "__main__":
    setup_project()
