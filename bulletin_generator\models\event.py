"""
Event data model
"""

from dataclasses import dataclass
from datetime import date
from typing import Optional
import sqlite3

@dataclass
class Event:
    """Represents a church event or announcement"""
    id: Optional[int]
    date: date  # Date the event appears in bulletin
    title: str
    description: Optional[str] = None
    event_date: Optional[date] = None  # Actual date of the event
    time: Optional[str] = None
    location: Optional[str] = None
    contact_person: Optional[str] = None
    contact_details: Optional[str] = None
    category: str = 'General'
    priority: int = 1
    
    @classmethod
    def from_db_row(cls, row: sqlite3.Row) -> 'Event':
        """Create Event from database row"""
        return cls(
            id=row['id'],
            date=date.fromisoformat(row['date']),
            title=row['title'],
            description=row['description'],
            event_date=date.fromisoformat(row['event_date']) if row['event_date'] else None,
            time=row['time'],
            location=row['location'],
            contact_person=row['contact_person'],
            contact_details=row['contact_details'],
            category=row['category'],
            priority=row['priority']
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'title': self.title,
            'description': self.description,
            'event_date': self.event_date.isoformat() if self.event_date else None,
            'time': self.time,
            'location': self.location,
            'contact_person': self.contact_person,
            'contact_details': self.contact_details,
            'category': self.category,
            'priority': self.priority
        }
    
    def get_formatted_description(self) -> str:
        """Get formatted description with time and location"""
        parts = []
        if self.description:
            parts.append(self.description)
        if self.time:
            parts.append(f"Time: {self.time}")
        if self.location:
            parts.append(f"Location: {self.location}")
        if self.contact_person:
            parts.append(f"Contact: {self.contact_person}")
        return " | ".join(parts)
    
    def __str__(self) -> str:
        """String representation"""
        event_date_str = f" ({self.event_date})" if self.event_date and self.event_date != self.date else ""
        return f"{self.title}{event_date_str}"
