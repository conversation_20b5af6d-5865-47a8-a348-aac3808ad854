#!/usr/bin/env python3
"""
Bulletin Date Updater Script
Updates all dates in the bulletin template for a specific publication date.
"""

import re
from datetime import datetime, timedelta
from pathlib import Path
import sys

def get_date_mappings(publication_date):
    """
    Generate all date mappings for a bulletin publication date.
    
    Args:
        publication_date: datetime object for the bulletin publication date (should be a Sunday)
    
    Returns:
        Dictionary of old_date -> new_date mappings
    """
    # Ensure publication date is a Sunday
    if publication_date.weekday() != 6:
        print(f"Warning: {publication_date.strftime('%Y-%m-%d')} is not a Sunday!")
    
    # Calculate related dates
    prev_sunday = publication_date - timedelta(days=7)
    next_sunday = publication_date + timedelta(days=7)
    following_sunday = publication_date + timedelta(days=14)
    
    # Days of the current week
    monday = publication_date + timedelta(days=1)
    tuesday = publication_date + timedelta(days=2)
    wednesday = publication_date + timedelta(days=3)
    thursday = publication_date + timedelta(days=4)
    friday = publication_date + timedelta(days=5)
    saturday = publication_date + timedelta(days=6)
    
    # Create mappings dictionary
    mappings = {}
    
    # Add various date format mappings
    # Format: DD-Mon (e.g., "17-Aug")
    mappings['bulletin_date_short'] = publication_date.strftime('%d-%b')
    mappings['next_sunday_short'] = next_sunday.strftime('%d-%b')
    
    # Format: DDth Month (e.g., "17th August")
    def get_ordinal(day):
        if 10 <= day % 100 <= 20:
            suffix = 'th'
        else:
            suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
        return f"{day}{suffix}"
    
    mappings['bulletin_date_ordinal'] = f"{get_ordinal(publication_date.day)} {publication_date.strftime('%B')}"
    mappings['next_sunday_ordinal'] = f"{get_ordinal(next_sunday.day)} {next_sunday.strftime('%B')}"
    mappings['following_sunday_ordinal'] = f"{get_ordinal(following_sunday.day)} {next_sunday.strftime('%B')}"
    
    # Week days
    mappings['monday'] = f"Monday, {get_ordinal(monday.day)} {monday.strftime('%B')}"
    mappings['tuesday'] = f"Tuesday, {get_ordinal(tuesday.day)} {tuesday.strftime('%B')}"
    mappings['wednesday'] = f"Wednesday, {get_ordinal(wednesday.day)} {wednesday.strftime('%B')}"
    mappings['thursday'] = f"Thursday, {get_ordinal(thursday.day)} {thursday.strftime('%B')}"
    mappings['friday'] = f"Friday, {get_ordinal(friday.day)} {friday.strftime('%B')}"
    mappings['saturday'] = f"Saturday, {get_ordinal(saturday.day)} {saturday.strftime('%B')}"
    
    # Full dates
    mappings['bulletin_date_full'] = publication_date.strftime('%A, %d %B %Y')
    mappings['next_sunday_full'] = next_sunday.strftime('%A, %d %B %Y')
    
    # Year if needed
    mappings['year'] = publication_date.strftime('%Y')
    
    return mappings, {
        'publication_date': publication_date,
        'prev_sunday': prev_sunday,
        'next_sunday': next_sunday,
        'following_sunday': following_sunday,
        'monday': monday,
        'tuesday': tuesday,
        'wednesday': wednesday,
        'thursday': thursday,
        'friday': friday,
        'saturday': saturday
    }

def find_and_replace_dates(content, publication_date):
    """
    Find and replace all dates in the bulletin content.
    """
    mappings, dates = get_date_mappings(publication_date)
    
    # More specific replacements first, then general patterns
    replacements = [
        # Specific known dates that should be updated
        # "this Sunday" references should be publication date
        (r'this Sunday, \d{1,2}(?:st|nd|rd|th)\s+\w+', f"this Sunday, {mappings['bulletin_date_ordinal']}"),
        (r'\b3rd August\b', mappings['bulletin_date_ordinal']),  # Old bulletin date
        (r'\b10-Aug\b', mappings['bulletin_date_short']),  # Old short date
        
        # "later on" or "services later on" should refer to next Sunday
        (r'services later on \d{1,2}(?:st|nd|rd|th)\s+\w+\s+\d{4}', 
         f"services later on {mappings['next_sunday_ordinal']} {dates['next_sunday'].year}"),
        
        # Keep "24th August 2025" as is if it's meant to be next Sunday
        # This is already correct for Aug 17 bulletin
        
        # Update weekday references (need more context to be accurate)
        (r'Saturday, 19 July', f"Saturday, {dates['saturday'].day} {dates['saturday'].strftime('%B')}"),
        
        # Service time dates - these should be bulletin date
        (r'(7\.45am Beachmere service .+?)\d{1,2}(?:st|nd|rd|th)\s+\w+', 
         rf'\1{mappings["bulletin_date_ordinal"]}'),
        
        # Update year references where appropriate
        (r'\b2024\b', mappings['year']),
        (r'\b2023\b', mappings['year']),
    ]
    
    updated_content = content
    for pattern, replacement in replacements:
        updated_content = re.sub(pattern, replacement, updated_content)
    
    return updated_content

def update_bulletin_file(input_file, output_file, publication_date):
    """
    Update dates in a bulletin file.
    """
    # Read the input file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update dates
    updated_content = find_and_replace_dates(content, publication_date)
    
    # Write to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"✓ Bulletin updated for {publication_date.strftime('%A, %d %B %Y')}")
    print(f"  Output saved to: {output_file}")

def main():
    """Main function to run the date updater."""
    if len(sys.argv) < 2:
        print("Usage: python update_bulletin_dates.py YYYY-MM-DD [input_file] [output_file]")
        print("Example: python update_bulletin_dates.py 2025-08-17")
        sys.exit(1)
    
    # Parse the publication date
    try:
        publication_date = datetime.strptime(sys.argv[1], '%Y-%m-%d')
    except ValueError:
        print(f"Error: Invalid date format. Please use YYYY-MM-DD")
        sys.exit(1)
    
    # Determine input and output files
    if len(sys.argv) >= 3:
        input_file = Path(sys.argv[2])
    else:
        # Default to the latest bulletin in output directory
        input_file = Path('output') / f"bulletin_{publication_date.strftime('%Y%m%d')}.svg"
    
    if len(sys.argv) >= 4:
        output_file = Path(sys.argv[3])
    else:
        output_file = Path('output') / f"bulletin_{publication_date.strftime('%Y%m%d')}_updated.svg"
    
    # Check if input file exists
    if not input_file.exists():
        print(f"Error: Input file not found: {input_file}")
        sys.exit(1)
    
    # Update the bulletin
    update_bulletin_file(input_file, output_file, publication_date)
    
    # Print date mappings for reference
    mappings, dates = get_date_mappings(publication_date)
    print("\nDate mappings used:")
    print(f"  Publication Date: {publication_date.strftime('%d-%b')} ({mappings['bulletin_date_ordinal']})")
    print(f"  Next Sunday: {dates['next_sunday'].strftime('%d-%b')} ({mappings['next_sunday_ordinal']})")
    print(f"  Week days: Mon-Sat {dates['monday'].strftime('%d')}-{dates['saturday'].strftime('%d')} {dates['monday'].strftime('%B')}")

if __name__ == '__main__':
    main()
