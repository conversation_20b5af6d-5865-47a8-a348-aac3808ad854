"""
Configuration settings for the Church Bulletin Generator
"""

import os
from pathlib import Path
from typing import Dict, Any

# Base paths
PROJECT_ROOT = Path(__file__).parent.parent
TEMPLATES_DIR = PROJECT_ROOT / "templates"
OUTPUT_DIR = PROJECT_ROOT / "output"
DATABASE_DIR = PROJECT_ROOT / "data"

# Database configuration
DATABASE_PATH = DATABASE_DIR / "bulletin.db"
DATABASE_SCHEMA_PATH = PROJECT_ROOT / "database_schema.sql"

# Template configuration
DEFAULT_TEMPLATE = "Bulletin 030825.svg"
TEMPLATE_ENCODING = "utf-8"

# Output configuration
OUTPUT_FORMATS = ["pdf", "svg", "png"]
DEFAULT_OUTPUT_FORMAT = "pdf"

# Date format settings
DATE_FORMAT = "%d-%b"  # e.g., "10-Aug"
FULL_DATE_FORMAT = "%d %B %Y"  # e.g., "10 August 2025"
DISPLAY_DATE_FORMAT = "%A, %d %B %Y"  # e.g., "Sunday, 10 August 2025"

# Service locations and default times
SERVICE_LOCATIONS = {
    "Beachmere": "7.45am",
    "Caboolture": "9.00am", 
    "Upper Caboolture": "10.45am",
    "Tongan Service": "1.00pm"
}

# Default contact information
DEFAULT_CONTACTS = {
    "minister": {
        "name": "Rev. Jason Grimsey",
        "phone": "0412 542 697",
        "email": "<EMAIL>",
        "availability": "Not available on Wednesdays or Saturdays"
    },
    "office": {
        "name": "Church Office",
        "email": "<EMAIL>"
    }
}

# Bank details for electronic giving
BANK_DETAILS = {
    "bsb": "334-040",
    "account_number": "5538 61353",
    "account_name": "UCA",
    "reference_note": "Please make a reference of which worship center the payment's for. ex: Caboolture Offering"
}

# Template field mappings
TEMPLATE_FIELDS = {
    # Date fields
    "bulletin_date": "date",
    "service_date": "date",
    
    # Service times
    "beachmere_time": "time",
    "caboolture_time": "time", 
    "upper_caboolture_time": "time",
    "tongan_service_time": "time",
    
    # People
    "preacher_name": "text",
    "minister_name": "text",
    "minister_phone": "text",
    "minister_email": "text",
    
    # Content
    "bible_verse": "text",
    "sermon_topic": "text",
    "weekly_theme": "text",
    
    # Events and announcements
    "events_list": "html",
    "diary_entries": "html",
    "special_announcements": "html"
}

# PDF generation settings
PDF_CONFIG = {
    "page_size": "A4",
    "margin": "1cm",
    "dpi": 300,
    "quality": "high"
}

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        },
    },
    "handlers": {
        "default": {
            "level": "INFO",
            "formatter": "standard",
            "class": "logging.StreamHandler",
        },
        "file": {
            "level": "DEBUG",
            "formatter": "standard",
            "class": "logging.FileHandler",
            "filename": "bulletin_generator.log",
            "mode": "a",
        },
    },
    "loggers": {
        "": {
            "handlers": ["default", "file"],
            "level": "DEBUG",
            "propagate": False
        }
    }
}

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary"""
    return {
        "paths": {
            "project_root": PROJECT_ROOT,
            "templates_dir": TEMPLATES_DIR,
            "output_dir": OUTPUT_DIR,
            "database_dir": DATABASE_DIR,
            "database_path": DATABASE_PATH,
            "database_schema_path": DATABASE_SCHEMA_PATH
        },
        "templates": {
            "default_template": DEFAULT_TEMPLATE,
            "encoding": TEMPLATE_ENCODING
        },
        "output": {
            "formats": OUTPUT_FORMATS,
            "default_format": DEFAULT_OUTPUT_FORMAT
        },
        "dates": {
            "date_format": DATE_FORMAT,
            "full_date_format": FULL_DATE_FORMAT,
            "display_date_format": DISPLAY_DATE_FORMAT
        },
        "services": {
            "locations": SERVICE_LOCATIONS
        },
        "contacts": DEFAULT_CONTACTS,
        "bank": BANK_DETAILS,
        "template_fields": TEMPLATE_FIELDS,
        "pdf": PDF_CONFIG,
        "logging": LOGGING_CONFIG
    }

def ensure_directories():
    """Ensure all required directories exist"""
    directories = [TEMPLATES_DIR, OUTPUT_DIR, DATABASE_DIR]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
