"""
Service Time data model
"""

from dataclasses import dataclass
from datetime import date
from typing import Optional
import sqlite3

@dataclass
class ServiceTime:
    """Represents a church service time"""
    id: Optional[int]
    date: date
    location: str
    time: str
    preacher: Optional[str] = None
    service_type: str = 'Regular'
    
    @classmethod
    def from_db_row(cls, row: sqlite3.Row) -> 'ServiceTime':
        """Create ServiceTime from database row"""
        return cls(
            id=row['id'],
            date=date.fromisoformat(row['date']),
            location=row['location'],
            time=row['time'],
            preacher=row['preacher'],
            service_type=row['service_type']
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'location': self.location,
            'time': self.time,
            'preacher': self.preacher,
            'service_type': self.service_type
        }
    
    def __str__(self) -> str:
        """String representation"""
        preacher_info = f" - {self.preacher}" if self.preacher else ""
        return f"{self.location} {self.time}{preacher_info}"
