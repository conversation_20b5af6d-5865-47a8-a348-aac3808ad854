# Church Bulletin Generator - Python Dependencies

# XML/SVG processing
lxml>=4.9.0

# PDF generation and conversion
reportlab>=4.0.0
cairosvg>=2.7.0
weasyprint>=60.0

# Image processing (for handling embedded images)
Pillow>=10.0.0

# Data validation and parsing
pydantic>=2.0.0

# CLI interface
click>=8.1.0
rich>=13.0.0  # For beautiful CLI output

# Configuration management
python-dotenv>=1.0.0
pyyaml>=6.0

# Date/time utilities
python-dateutil>=2.8.0

# Optional: Web interface dependencies (for future use)
# flask>=2.3.0
# flask-cors>=4.0.0
# flask-sqlalchemy>=3.0.0

# Development dependencies
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0
