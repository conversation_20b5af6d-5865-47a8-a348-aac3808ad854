"""
Date utility functions
"""

from datetime import date, datetime, timedelta
from typing import Optional

def format_date(date_obj: date, format_type: str = 'short') -> str:
    """Format date according to bulletin standards"""
    if format_type == 'short':
        return date_obj.strftime('%d-%b')  # 10-Aug
    elif format_type == 'full':
        return date_obj.strftime('%A, %d %B %Y')  # Sunday, 10 August 2025
    elif format_type == 'medium':
        return date_obj.strftime('%d %B %Y')  # 10 August 2025
    else:
        return str(date_obj)

def parse_date(date_string: str) -> Optional[date]:
    """Parse date string in various formats"""
    formats = [
        '%Y-%m-%d',      # 2025-08-10
        '%d-%m-%Y',      # 10-08-2025
        '%d/%m/%Y',      # 10/08/2025
        '%d-%b-%Y',      # 10-Aug-2025
        '%d %B %Y',      # 10 August 2025
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_string, fmt).date()
        except ValueError:
            continue
    
    return None

def get_next_sunday(from_date: Optional[date] = None) -> date:
    """Get the next Sunday from given date (or today)"""
    if from_date is None:
        from_date = date.today()
    
    days_ahead = 6 - from_date.weekday()  # Sunday is 6
    if days_ahead <= 0:  # Target day already happened this week
        days_ahead += 7
    
    return from_date + timedelta(days_ahead)

def get_previous_sunday(from_date: Optional[date] = None) -> date:
    """Get the previous Sunday from given date (or today)"""
    if from_date is None:
        from_date = date.today()
    
    days_back = from_date.weekday() + 1  # Sunday is 6, so Monday is 0
    if from_date.weekday() == 6:  # If it's already Sunday
        days_back = 7
    
    return from_date - timedelta(days_back)

def is_sunday(date_obj: date) -> bool:
    """Check if date is a Sunday"""
    return date_obj.weekday() == 6

def get_sundays_in_month(year: int, month: int) -> list[date]:
    """Get all Sundays in a given month"""
    sundays = []
    
    # Start from first day of month
    current_date = date(year, month, 1)
    
    # Find first Sunday
    while current_date.weekday() != 6:
        current_date += timedelta(days=1)
        if current_date.month != month:
            return sundays
    
    # Add all Sundays in the month
    while current_date.month == month:
        sundays.append(current_date)
        current_date += timedelta(days=7)
    
    return sundays
